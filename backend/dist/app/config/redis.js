"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testRedisConnection = exports.otpOperations = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const index_1 = __importDefault(require("./index"));
// Create Redis client instance
const redis = new ioredis_1.default({
    host: index_1.default.redis.host,
    port: index_1.default.redis.port,
    password: index_1.default.redis.password,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
    lazyConnect: true,
    // TLS configuration for Upstash Redis
    tls: index_1.default.redis.host.includes('upstash.io') ? {} : undefined,
});
// Handle Redis connection events
redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
});
redis.on('ready', () => {
    console.log('✅ Redis is ready to accept commands');
});
redis.on('error', (error) => {
    console.error('❌ Redis connection error:', error);
});
redis.on('close', () => {
    console.log('⚠️ Redis connection closed');
});
redis.on('reconnecting', () => {
    console.log('🔄 Redis reconnecting...');
});
// OTP-related Redis operations
exports.otpOperations = {
    // Store OTP with TTL (5 minutes = 300 seconds)
    setOTP(email_1, otp_1) {
        return __awaiter(this, arguments, void 0, function* (email, otp, ttlSeconds = 300) {
            const key = `otp:${email}`;
            yield redis.setex(key, ttlSeconds, otp);
            console.log(`✅ OTP stored for ${email} with TTL ${ttlSeconds}s`);
        });
    },
    // Get OTP for email
    getOTP(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp:${email}`;
            const otp = yield redis.get(key);
            return otp;
        });
    },
    // Delete OTP after successful verification
    deleteOTP(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp:${email}`;
            yield redis.del(key);
            console.log(`✅ OTP deleted for ${email}`);
        });
    },
    // Check if OTP exists and get TTL
    getOTPTTL(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp:${email}`;
            return yield redis.ttl(key);
        });
    },
    // Rate limiting for OTP requests (max 3 requests per 15 minutes)
    checkRateLimit(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp_rate_limit:${email}`;
            const current = yield redis.get(key);
            const maxRequests = 3;
            const windowSeconds = 900; // 15 minutes
            if (!current) {
                yield redis.setex(key, windowSeconds, '1');
                return { allowed: true, remaining: maxRequests - 1, resetTime: Date.now() + (windowSeconds * 1000) };
            }
            const count = parseInt(current);
            if (count >= maxRequests) {
                const ttl = yield redis.ttl(key);
                return { allowed: false, remaining: 0, resetTime: Date.now() + (ttl * 1000) };
            }
            yield redis.incr(key);
            const ttl = yield redis.ttl(key);
            return { allowed: true, remaining: maxRequests - (count + 1), resetTime: Date.now() + (ttl * 1000) };
        });
    },
    // Utility functions for testing and debugging
    clearOTP(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp:${email}`;
            yield redis.del(key);
            console.log(`✅ OTP cleared for ${email}`);
        });
    },
    clearRateLimit(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const key = `otp_rate_limit:${email}`;
            yield redis.del(key);
            console.log(`✅ Rate limit cleared for ${email}`);
        });
    },
    // Clear all OTP-related data for an email (useful for testing)
    clearAllOTPData(email) {
        return __awaiter(this, void 0, void 0, function* () {
            yield Promise.all([
                this.clearOTP(email),
                this.clearRateLimit(email)
            ]);
            console.log(`✅ All OTP data cleared for ${email}`);
        });
    }
};
// Test Redis connection
const testRedisConnection = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield redis.ping();
        console.log('✅ Redis connection test successful');
        return true;
    }
    catch (error) {
        console.error('❌ Redis connection test failed:', error);
        return false;
    }
});
exports.testRedisConnection = testRedisConnection;
exports.default = redis;
