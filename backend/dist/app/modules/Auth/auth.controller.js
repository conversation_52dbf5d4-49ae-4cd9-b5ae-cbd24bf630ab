"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthControllers = void 0;
const config_1 = __importDefault(require("../../config"));
const catchAsync_1 = __importDefault(require("../../utils/catchAsync"));
const sendResponse_1 = __importDefault(require("../../utils/sendResponse"));
const auth_service_1 = require("./auth.service");
const http_status_1 = __importDefault(require("http-status"));
const loginUser = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const result = yield auth_service_1.AuthServices.loginUser(req.body);
    const { refreshToken, accessToken, user } = result;
    // Get domain from request origin or use default
    const origin = req.get('origin');
    let domain;
    if (origin && config_1.default.NODE_ENV === 'production') {
        try {
            // Extract domain from origin (e.g., https://example.com -> example.com)
            domain = new URL(origin || '').hostname;
            // If it's not localhost, ensure we have the root domain for cookies
            if (!domain.includes('localhost')) {
                // Handle subdomains by getting the root domain
                const parts = domain.split('.');
                if (parts.length > 2) {
                    domain = parts.slice(-2).join('.');
                }
            }
        }
        catch (error) {
            console.error('Error parsing origin for cookie domain:', error);
        }
    }
    console.log(`Setting refresh token cookie with domain: ${domain || 'not set'}, sameSite: ${config_1.default.NODE_ENV === 'production' ? 'none' : 'lax'}`);
    res.cookie('refreshToken', refreshToken, {
        secure: true, // Always use secure in modern browsers
        httpOnly: true,
        sameSite: config_1.default.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' for cross-site in production
        domain: domain || undefined, // Set domain in production
        maxAge: 1000 * 60 * 60 * 24 * 365, // 1 year
    });
    // Also include the refresh token in the response for the client to store as a fallback
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'User is logged in successfully!',
        data: {
            accessToken,
            refreshToken, // Include refresh token in response for client-side storage
            user,
        },
    });
}));
const changePassword = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const passwordData = __rest(req.body, []);
    const result = yield auth_service_1.AuthServices.changePassword(req.user, passwordData);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Password is updated successfully!',
        data: result,
    });
}));
const refreshToken = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    // Try to get refresh token from multiple sources
    const tokenFromCookie = (_a = req.cookies) === null || _a === void 0 ? void 0 : _a.refreshToken;
    const tokenFromBody = (_b = req.body) === null || _b === void 0 ? void 0 : _b.refreshToken;
    const tokenFromHeader = req.headers['x-refresh-token'];
    // Also check for Authorization header with Bearer format
    const authHeader = (_c = req.headers) === null || _c === void 0 ? void 0 : _c.authorization;
    let tokenFromBearer;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        tokenFromBearer = authHeader.split(' ')[1];
    }
    const refreshToken = tokenFromCookie || tokenFromBody || tokenFromHeader || tokenFromBearer;
    if (!refreshToken) {
        console.error('Refresh token missing from all sources');
        console.log('Headers:', JSON.stringify(req.headers));
        console.log('Cookies:', JSON.stringify(req.cookies));
        console.log('Body:', JSON.stringify(req.body));
        return (0, sendResponse_1.default)(res, {
            statusCode: http_status_1.default.UNAUTHORIZED,
            success: false,
            message: 'Refresh token is required!',
            data: null,
        });
    }
    const tokenSource = tokenFromCookie ? 'cookie' :
        tokenFromBody ? 'body' :
            tokenFromBearer ? 'bearer' :
                'header';
    console.log(`Refresh token source: ${tokenSource}, token length: ${refreshToken.length}`);
    try {
        // Trim the token to remove any whitespace
        const cleanToken = refreshToken.trim();
        if (!cleanToken || cleanToken.length < 10) {
            throw new Error('Invalid refresh token format');
        }
        const result = yield auth_service_1.AuthServices.refreshToken(cleanToken);
        // Set the refresh token in a cookie as well for redundancy
        if (result.refreshToken) {
            res.cookie('refreshToken', result.refreshToken, {
                secure: true,
                httpOnly: true,
                sameSite: config_1.default.NODE_ENV === 'production' ? 'none' : 'lax',
                domain: req.get('origin') ? new URL(req.get('origin') || '').hostname : undefined,
                maxAge: 1000 * 60 * 60 * 24 * 30, // 30 days
            });
        }
        (0, sendResponse_1.default)(res, {
            statusCode: http_status_1.default.OK,
            success: true,
            message: 'Access token is retrieved successfully!',
            data: result,
        });
    }
    catch (error) {
        console.error('Error refreshing token:', error);
        // Clear the invalid refresh token cookie
        res.clearCookie('refreshToken', {
            secure: true,
            httpOnly: true,
            sameSite: config_1.default.NODE_ENV === 'production' ? 'none' : 'lax',
        });
        (0, sendResponse_1.default)(res, {
            statusCode: http_status_1.default.UNAUTHORIZED,
            success: false,
            message: error instanceof Error ? error.message : 'Invalid refresh token',
            data: null,
        });
    }
}));
const forgetPassword = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const email = req.body.email;
    const result = yield auth_service_1.AuthServices.forgetPassword(email);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Password reset link is sent to your email!',
        data: result,
    });
}));
const resetPassword = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const token = req.headers.authorization;
    const result = yield auth_service_1.AuthServices.resetPassword(req.body, token);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Password reset successfully!',
        data: result,
    });
}));
const logoutUser = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { refreshToken } = req.cookies;
    yield auth_service_1.AuthServices.logoutUser(refreshToken);
    // Get domain from request origin or use default
    const origin = req.get('origin');
    let domain;
    if (origin && config_1.default.NODE_ENV === 'production') {
        try {
            // Extract domain from origin (e.g., https://example.com -> example.com)
            domain = new URL(origin || '').hostname;
            // If it's not localhost, ensure we have the root domain for cookies
            if (!domain.includes('localhost')) {
                // Handle subdomains by getting the root domain
                const parts = domain.split('.');
                if (parts.length > 2) {
                    domain = parts.slice(-2).join('.');
                }
            }
        }
        catch (error) {
            console.error('Error parsing origin for cookie domain:', error);
        }
    }
    console.log(`Clearing refresh token cookie with domain: ${domain || 'not set'}, sameSite: ${config_1.default.NODE_ENV === 'production' ? 'none' : 'lax'}`);
    res.clearCookie('refreshToken', {
        secure: true, // Always use secure in modern browsers
        httpOnly: true,
        sameSite: config_1.default.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' for cross-site in production
        domain: domain || undefined, // Set domain in production
    });
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'User is logged out successfully!',
        data: null,
    });
}));
const verifyEmail = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { email, code } = req.body;
    const result = yield auth_service_1.AuthServices.verifyEmail(email, code);
    // Set refresh token as cookie
    res.cookie('refreshToken', result.refreshToken, {
        secure: config_1.default.NODE_ENV === 'production',
        httpOnly: true,
        sameSite: 'lax',
        maxAge: 1000 * 60 * 60 * 24 * 365,
    });
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: result.message,
        data: {
            user: result.user,
            accessToken: result.accessToken,
        },
    });
}));
const resendVerificationEmail = (0, catchAsync_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { email } = req.body;
    const result = yield auth_service_1.AuthServices.resendVerificationEmail(email);
    (0, sendResponse_1.default)(res, {
        statusCode: http_status_1.default.OK,
        success: true,
        message: 'Verification email sent successfully!',
        data: result,
    });
}));
exports.AuthControllers = {
    loginUser,
    changePassword,
    refreshToken,
    forgetPassword,
    resetPassword,
    logoutUser,
    verifyEmail,
    resendVerificationEmail,
};
