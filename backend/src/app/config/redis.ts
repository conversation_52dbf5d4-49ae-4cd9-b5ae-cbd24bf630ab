import Redis from 'ioredis';
import config from './index';

// Create Redis client instance
const redis = new Redis({
  host: config.redis.host,
  port: config.redis.port,
  password: config.redis.password,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
  // TLS configuration for Upstash Redis
  tls: config.redis.host.includes('upstash.io') ? {} : undefined,
});

// Handle Redis connection events
redis.on('connect', () => {
  console.log('✅ Redis connected successfully');
});

redis.on('ready', () => {
  console.log('✅ Redis is ready to accept commands');
});

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error);
});

redis.on('close', () => {
  console.log('⚠️ Redis connection closed');
});

redis.on('reconnecting', () => {
  console.log('🔄 Redis reconnecting...');
});

// OTP-related Redis operations
export const otpOperations = {
  // Store OTP with TTL (5 minutes = 300 seconds)
  async setOTP(email: string, otp: string, ttlSeconds: number = 300): Promise<void> {
    const key = `otp:${email}`;
    await redis.setex(key, ttlSeconds, otp);
    console.log(`✅ OTP stored for ${email} with TTL ${ttlSeconds}s`);
  },

  // Get OTP for email
  async getOTP(email: string): Promise<string | null> {
    const key = `otp:${email}`;
    const otp = await redis.get(key);
    return otp;
  },

  // Delete OTP after successful verification
  async deleteOTP(email: string): Promise<void> {
    const key = `otp:${email}`;
    await redis.del(key);
    console.log(`✅ OTP deleted for ${email}`);
  },

  // Check if OTP exists and get TTL
  async getOTPTTL(email: string): Promise<number> {
    const key = `otp:${email}`;
    return await redis.ttl(key);
  },

  // Rate limiting for OTP requests (max 3 requests per 15 minutes)
  async checkRateLimit(email: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = `otp_rate_limit:${email}`;
    const current = await redis.get(key);
    const maxRequests = 3;
    const windowSeconds = 900; // 15 minutes

    if (!current) {
      await redis.setex(key, windowSeconds, '1');
      return { allowed: true, remaining: maxRequests - 1, resetTime: Date.now() + (windowSeconds * 1000) };
    }

    const count = parseInt(current);
    if (count >= maxRequests) {
      const ttl = await redis.ttl(key);
      return { allowed: false, remaining: 0, resetTime: Date.now() + (ttl * 1000) };
    }

    await redis.incr(key);
    const ttl = await redis.ttl(key);
    return { allowed: true, remaining: maxRequests - (count + 1), resetTime: Date.now() + (ttl * 1000) };
  },

  // Utility functions for testing and debugging
  async clearOTP(email: string): Promise<void> {
    const key = `otp:${email}`;
    await redis.del(key);
    console.log(`✅ OTP cleared for ${email}`);
  },

  async clearRateLimit(email: string): Promise<void> {
    const key = `otp_rate_limit:${email}`;
    await redis.del(key);
    console.log(`✅ Rate limit cleared for ${email}`);
  },

  // Clear all OTP-related data for an email (useful for testing)
  async clearAllOTPData(email: string): Promise<void> {
    await Promise.all([
      this.clearOTP(email),
      this.clearRateLimit(email)
    ]);
    console.log(`✅ All OTP data cleared for ${email}`);
  }
};

// Test Redis connection
export const testRedisConnection = async (): Promise<boolean> => {
  try {
    await redis.ping();
    console.log('✅ Redis connection test successful');
    return true;
  } catch (error) {
    console.error('❌ Redis connection test failed:', error);
    return false;
  }
};

export default redis;
