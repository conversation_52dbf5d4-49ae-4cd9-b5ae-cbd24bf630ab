import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import { ArrowLeft, Mail, Loader2, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAppDispatch } from "@/redux/hooks";
import { setUser, TUser } from "@/redux/features/auth/authSlice";
import { 
  useVerifyEmailMutation, 
  useResendVerificationEmailMutation 
} from "@/redux/features/auth/authApi";

const OTPVerificationPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const email = searchParams.get("email");
  const [otp, setOtp] = useState("");
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const [canResend, setCanResend] = useState(false);
  
  const [verifyEmail, { isLoading: isVerifying }] = useVerifyEmailMutation();
  const [resendVerification, { isLoading: isResending }] = useResendVerificationEmailMutation();

  // Redirect if no email provided
  useEffect(() => {
    if (!email) {
      toast.error("No email provided. Please sign up again.");
      navigate("/signup");
    }
  }, [email, navigate]);

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleVerifyOTP = async () => {
    if (!email || !otp) {
      toast.error("Please enter the verification code");
      return;
    }

    if (otp.length !== 6) {
      toast.error("Verification code must be 6 digits");
      return;
    }

    const toastId = toast.loading("Verifying...");

    try {
      const result = await verifyEmail({ email, code: otp }).unwrap();
      
      // Set user in Redux store after successful verification
      const user: TUser = {
        email: result.data.user.email,
        name: result.data.user.name || { firstName: "", lastName: "" },
        photoUrl: result.data.user.photoUrl || "",
        role: result.data.user.role,
        isDeleted: result.data.user.isDeleted || false,
        isVerified: true,
        createdAt: result.data.user.createdAt,
        updatedAt: result.data.user.updatedAt,
      };

      dispatch(setUser({ user, token: result.data.accessToken }));

      toast.success("Email verified successfully!", {
        id: toastId,
        duration: 2000,
      });

      // Navigate based on user role
      if (result.data.user.role === "teacher") {
        navigate("/teacher/dashboard");
      } else if (result.data.user.role === "student") {
        navigate("/student/dashboard");
      } else {
        navigate("/");
      }
    } catch (error: any) {
      console.error("OTP verification error:", error);
      toast.error(error?.data?.message || "Verification failed. Please try again.", {
        id: toastId,
        duration: 3000,
      });
    }
  };

  const handleResendOTP = async () => {
    if (!email) return;

    const toastId = toast.loading("Sending new verification code...");

    try {
      await resendVerification({ email }).unwrap();
      
      toast.success("New verification code sent to your email!", {
        id: toastId,
        duration: 3000,
      });
      
      // Reset timer
      setTimeLeft(600);
      setCanResend(false);
      setOtp("");
    } catch (error: any) {
      console.error("Resend OTP error:", error);
      toast.error(error?.data?.message || "Failed to resend verification code.", {
        id: toastId,
        duration: 3000,
      });
    }
  };

  const handleOTPChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ""); // Only allow digits
    if (value.length <= 6) {
      setOtp(value);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && otp.length === 6) {
      handleVerifyOTP();
    }
  };

  if (!email) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <Mail className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold">Verify Your Email</CardTitle>
          <CardDescription className="text-gray-600">
            We've sent a 6-digit verification code to
            <br />
            <span className="font-medium text-gray-900">{email}</span>
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="otp" className="text-sm font-medium text-gray-700">
              Verification Code
            </label>
            <Input
              id="otp"
              type="text"
              placeholder="Enter 6-digit code"
              value={otp}
              onChange={handleOTPChange}
              onKeyPress={handleKeyPress}
              className="text-center text-lg font-mono tracking-widest"
              maxLength={6}
              autoComplete="one-time-code"
            />
          </div>

          <Button
            onClick={handleVerifyOTP}
            disabled={isVerifying || otp.length !== 6}
            className="w-full"
          >
            {isVerifying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify Email"
            )}
          </Button>

          <div className="text-center space-y-3">
            {timeLeft > 0 ? (
              <p className="text-sm text-gray-600">
                Code expires in{" "}
                <span className="font-medium text-red-600">
                  {formatTime(timeLeft)}
                </span>
              </p>
            ) : (
              <p className="text-sm text-red-600 font-medium">
                Verification code has expired
              </p>
            )}

            <Button
              variant="ghost"
              onClick={handleResendOTP}
              disabled={isResending || (!canResend && timeLeft > 0)}
              className="text-green-600 hover:text-green-700"
            >
              {isResending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Resend Code
                </>
              )}
            </Button>
          </div>

          <div className="pt-4 border-t">
            <Button
              variant="ghost"
              onClick={() => navigate("/signup")}
              className="w-full text-gray-600 hover:text-gray-700"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sign Up
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OTPVerificationPage;
